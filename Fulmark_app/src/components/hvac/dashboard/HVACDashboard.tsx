'use client';

// 🌟 HVAC Dashboard - PEŁNA MOC WIATRU! ⚡
// Complete dashboard with cosmic-level UX and AI insights

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  LayoutDashboard,
  Users,
  Settings,
  Cloud,
  BarChart,
  ChevronRight,
  Wrench,
  Zap,
  Package,
  FileText,
  BarChart2,
  Activity
} from 'lucide-react';
import BusinessIntelligence from './BusinessIntelligence';
import WeatherIntegration from './WeatherIntegration';
import PredictiveMaintenance from './PredictiveMaintenance';
import CustomerAnalytics from './CustomerAnalytics';
import ServiceManagement from './ServiceManagement';
import EnergyOptimization from './EnergyOptimization';
import SystemMonitoring from './SystemMonitoring';

const HVACDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // Mock data for Business Intelligence
  const businessIntelligenceData = {
    revenue: {
      current: 150000,
      growth: 25,
      history: [
        { date: '2024-01', amount: 120000 },
        { date: '2024-02', amount: 130000 },
        { date: '2024-03', amount: 140000 },
        { date: '2024-04', amount: 150000 }
      ]
    },
    profit: {
      current: 45000,
      margin: 30,
      history: [
        { date: '2024-01', amount: 36000 },
        { date: '2024-02', amount: 39000 },
        { date: '2024-03', amount: 42000 },
        { date: '2024-04', amount: 45000 }
      ]
    },
    customers: {
      total: 250,
      retention: 85,
      segments: [
        { name: 'Residential', value: 150 },
        { name: 'Commercial', value: 80 },
        { name: 'Industrial', value: 20 }
      ]
    },
    efficiency: {
      utilization: 85,
      responseTime: 90,
      metrics: [
        { name: 'Energy Efficiency', value: 85 },
        { name: 'Operational Efficiency', value: 90 },
        { name: 'Maintenance Efficiency', value: 88 }
      ]
    }
  };

  // Mock data for Weather Integration
  const weatherData = {
    current: {
      temperature: 22,
      feelsLike: 24,
      humidity: 65,
      windSpeed: 15,
      windDirection: 'NE',
      condition: 'sunny',
      icon: 'sun'
    },
    forecast: [
      {
        date: '2024-04-01',
        temperature: { min: 18, max: 25 },
        condition: 'sunny',
        icon: 'sun',
        precipitation: 10,
        windSpeed: 12
      },
      {
        date: '2024-04-02',
        temperature: { min: 17, max: 23 },
        condition: 'cloudy',
        icon: 'cloud',
        precipitation: 30,
        windSpeed: 18
      },
      {
        date: '2024-04-03',
        temperature: { min: 16, max: 22 },
        condition: 'rain',
        icon: 'rain',
        precipitation: 60,
        windSpeed: 20
      }
    ],
    alerts: [
      {
        type: 'warning' as const,
        message: 'Silne wiatry przewidywane w ciągu najbliższych 24 godzin',
        severity: 'medium' as const
      }
    ],
    hvacRecommendations: [
      {
        type: 'cooling' as const,
        recommendation: 'Zwiększ wydajność chłodzenia w godzinach szczytu',
        impact: 'high' as const
      },
      {
        type: 'ventilation' as const,
        recommendation: 'Dostosuj wentylację do warunków wietrznych',
        impact: 'medium' as const
      }
    ]
  };

  // Mock data for Predictive Maintenance
  const maintenanceData = {
    systemHealth: {
      overall: 92,
      components: [
        {
          name: 'Sprężarka',
          health: 95,
          status: 'optimal' as const,
          lastMaintenance: '2024-03-15',
          nextMaintenance: '2024-06-15'
        },
        {
          name: 'Wentylator',
          health: 88,
          status: 'warning' as const,
          lastMaintenance: '2024-02-20',
          nextMaintenance: '2024-05-20'
        },
        {
          name: 'Filtry',
          health: 75,
          status: 'critical' as const,
          lastMaintenance: '2024-01-10',
          nextMaintenance: '2024-04-10'
        }
      ]
    },
    predictiveAlerts: [
      {
        id: '1',
        component: 'Filtry',
        type: 'replacement' as const,
        severity: 'high' as const,
        predictedDate: '2024-04-15',
        description: 'Wymagana wymiana filtrów',
        impact: 'Wysoka - może wpłynąć na wydajność systemu'
      }
    ],
    maintenanceHistory: [
      {
        id: '1',
        date: '2024-03-15',
        type: 'Konserwacja rutynowa',
        description: 'Przegląd i czyszczenie sprężarki',
        technician: 'Jan Kowalski',
        cost: 1200,
        status: 'completed' as const
      }
    ],
    efficiencyMetrics: {
      energyEfficiency: 88,
      operationalEfficiency: 92,
      maintenanceEfficiency: 90,
      costSavings: 15000
    }
  };

  // Mock data for Customer Analytics
  const customerData = {
    overview: {
      totalCustomers: 250,
      activeCustomers: 200,
      newCustomers: 30,
      churnRate: 5
    },
    satisfaction: {
      averageRating: 4.5,
      ratingDistribution: [
        { rating: 5, count: 120 },
        { rating: 4, count: 80 },
        { rating: 3, count: 30 },
        { rating: 2, count: 15 },
        { rating: 1, count: 5 }
      ],
      recentReviews: [
        {
          id: '1',
          customer: 'Anna Nowak',
          rating: 5,
          comment: 'Świetna obsługa i profesjonalne podejście',
          date: '2024-03-28'
        }
      ]
    },
    journey: {
      stages: [
        { name: 'Kontakt', count: 50, conversionRate: 100 },
        { name: 'Konsultacja', count: 40, conversionRate: 80 },
        { name: 'Oferta', count: 35, conversionRate: 70 },
        { name: 'Realizacja', count: 30, conversionRate: 60 }
      ],
      recentActivities: [
        {
          id: '1',
          customer: 'Jan Kowalski',
          action: 'Złożono zapytanie o ofertę',
          stage: 'Kontakt',
          date: '2024-03-29'
        }
      ]
    },
    revenue: {
      totalRevenue: 150000,
      averageRevenue: 600,
      revenueByService: [
        { service: 'Instalacja', amount: 60000, percentage: 40 },
        { service: 'Konserwacja', amount: 45000, percentage: 30 },
        { service: 'Naprawy', amount: 30000, percentage: 20 },
        { service: 'Konsultacje', amount: 15000, percentage: 10 }
      ],
      monthlyTrend: [
        { month: 'Sty', revenue: 120000, customers: 200 },
        { month: 'Lut', revenue: 130000, customers: 210 },
        { month: 'Mar', revenue: 140000, customers: 220 },
        { month: 'Kwi', revenue: 150000, customers: 250 }
      ]
    }
  };

  // Mock data for Service Management
  const serviceData = {
    tickets: [
      {
        id: 'T001',
        customer: 'Jan Kowalski',
        type: 'maintenance' as const,
        status: 'in-progress' as const,
        priority: 'high' as const,
        assignedTo: 'Adam Nowak',
        scheduledDate: '2024-04-01',
        location: 'Warszawa',
        description: 'Konserwacja klimatyzacji'
      },
      {
        id: 'T002',
        customer: 'Anna Wiśniewska',
        type: 'repair' as const,
        status: 'pending' as const,
        priority: 'urgent' as const,
        assignedTo: 'Piotr Kowalczyk',
        scheduledDate: '2024-04-02',
        location: 'Kraków',
        description: 'Awaria ogrzewania'
      }
    ],
    technicians: [
      {
        id: 'T001',
        name: 'Adam Nowak',
        specialization: ['HVAC', 'Klimatyzacja'],
        status: 'busy' as const,
        currentLocation: 'Warszawa',
        rating: 4.8,
        completedJobs: 150
      },
      {
        id: 'T002',
        name: 'Piotr Kowalczyk',
        specialization: ['Ogrzewanie', 'Wentylacja'],
        status: 'available' as const,
        currentLocation: 'Kraków',
        rating: 4.9,
        completedJobs: 200
      }
    ],
    metrics: {
      totalTickets: 50,
      openTickets: 15,
      completedTickets: 35,
      averageResolutionTime: 4.5,
      customerSatisfaction: 95,
      ticketDistribution: [
        { type: 'Instalacja', count: 20 },
        { type: 'Konserwacja', count: 15 },
        { type: 'Naprawa', count: 10 },
        { type: 'Inspekcja', count: 5 }
      ],
      priorityDistribution: [
        { priority: 'Niski', count: 15 },
        { priority: 'Średni', count: 20 },
        { priority: 'Wysoki', count: 10 },
        { priority: 'Pilny', count: 5 }
      ]
    }
  };

  // Mock data for Energy Optimization
  const energyData = {
    consumption: [
      { timestamp: '2024-03-25', value: 1200, cost: 600, type: 'total' as const },
      { timestamp: '2024-03-26', value: 1150, cost: 575, type: 'total' as const },
      { timestamp: '2024-03-27', value: 1300, cost: 650, type: 'total' as const },
      { timestamp: '2024-03-28', value: 1250, cost: 625, type: 'total' as const }
    ],
    recommendations: [
      {
        id: 'R001',
        type: 'energy' as const,
        priority: 'high' as const,
        title: 'Optymalizacja wentylacji',
        description: 'Dostosowanie wentylacji do aktualnych warunków pogodowych',
        potentialSavings: 5000,
        implementationCost: 2000,
        paybackPeriod: 5
      },
      {
        id: 'R002',
        type: 'efficiency' as const,
        priority: 'medium' as const,
        title: 'Modernizacja sterowania',
        description: 'Wdrożenie inteligentnego systemu sterowania HVAC',
        potentialSavings: 8000,
        implementationCost: 15000,
        paybackPeriod: 23
      }
    ],
    metrics: {
      currentConsumption: 1250,
      averageConsumption: 1225,
      peakConsumption: 1500,
      costSavings: 15000,
      efficiencyScore: 88,
      carbonReduction: 2500
    }
  };

  // Add mock data for System Monitoring
  const systemMonitoringData = {
    metrics: [
      {
        id: '1',
        name: 'CPU Usage',
        value: 45,
        unit: '%',
        status: 'normal' as const,
        trend: 'stable' as const,
        threshold: {
          warning: 70,
          critical: 90,
        },
      },
      {
        id: '2',
        name: 'Memory Usage',
        value: 60,
        unit: '%',
        status: 'warning' as const,
        trend: 'up' as const,
        threshold: {
          warning: 65,
          critical: 85,
        },
      },
      {
        id: '3',
        name: 'Network Load',
        value: 30,
        unit: '%',
        status: 'normal' as const,
        trend: 'down' as const,
        threshold: {
          warning: 75,
          critical: 90,
        },
      },
      {
        id: '4',
        name: 'Storage Usage',
        value: 75,
        unit: '%',
        status: 'warning' as const,
        trend: 'up' as const,
        threshold: {
          warning: 70,
          critical: 85,
        },
      },
    ],
    alerts: [
      {
        id: '1',
        type: 'warning' as const,
        message: 'High memory usage detected',
        timestamp: '2024-02-20 10:30:00',
        source: 'System Monitor',
        status: 'active' as const,
        priority: 'medium' as const,
      },
      {
        id: '2',
        type: 'error' as const,
        message: 'Database connection timeout',
        timestamp: '2024-02-20 10:15:00',
        source: 'Database Service',
        status: 'active' as const,
        priority: 'high' as const,
      },
      {
        id: '3',
        type: 'info' as const,
        message: 'System backup completed',
        timestamp: '2024-02-20 09:00:00',
        source: 'Backup Service',
        status: 'resolved' as const,
        priority: 'low' as const,
      },
    ],
    status: {
      overall: 'degraded' as const,
      components: [
        {
          name: 'Database Server',
          status: 'operational' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'Application Server',
          status: 'degraded' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'File Storage',
          status: 'degraded' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
        {
          name: 'Network Services',
          status: 'operational' as const,
          lastChecked: '2024-02-20 10:30:00',
        },
      ],
    },
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: LayoutDashboard },
    { id: 'analytics', label: 'Analytics', icon: BarChart },
    { id: 'maintenance', label: 'Maintenance', icon: Settings },
    { id: 'weather', label: 'Weather', icon: Cloud },
    { id: 'customers', label: 'Customers', icon: Users },
    { id: 'service', label: 'Service', icon: Wrench },
    { id: 'energy', label: 'Energy', icon: Zap },
    { id: 'inventory', label: 'Inventory', icon: Package },
    { id: 'documents', label: 'Documents', icon: FileText },
    { id: 'reports', label: 'Reports', icon: BarChart2 },
    { id: 'system', label: 'System', icon: Activity },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <BusinessIntelligence data={businessIntelligenceData} />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <WeatherIntegration data={weatherData} />
              <PredictiveMaintenance data={maintenanceData} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <ServiceManagement
                tickets={serviceData.tickets}
                technicians={serviceData.technicians}
                metrics={serviceData.metrics}
              />
              <EnergyOptimization
                consumption={energyData.consumption}
                recommendations={energyData.recommendations}
                metrics={energyData.metrics}
              />
            </div>
          </div>
        );
      case 'analytics':
        return <BusinessIntelligence data={businessIntelligenceData} />;
      case 'maintenance':
        return <PredictiveMaintenance data={maintenanceData} />;
      case 'weather':
        return <WeatherIntegration data={weatherData} />;
      case 'customers':
        return <CustomerAnalytics data={customerData} />;
      case 'service':
        return (
          <ServiceManagement
            tickets={serviceData.tickets}
            technicians={serviceData.technicians}
            metrics={serviceData.metrics}
          />
        );
      case 'energy':
        return (
          <EnergyOptimization
            consumption={energyData.consumption}
            recommendations={energyData.recommendations}
            metrics={energyData.metrics}
          />
        );
      case 'system':
        return <SystemMonitoring {...systemMonitoringData} />;
      default:
        return <div>Select a tab to view content</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-sm">
          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6" aria-label="Tabs">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm
                      ${activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }
                    `}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.label}</span>
                    {activeTab === tab.id && (
                      <ChevronRight className="w-4 h-4" />
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HVACDashboard;